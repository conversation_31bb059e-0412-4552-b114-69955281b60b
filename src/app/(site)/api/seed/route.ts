import { getPayload } from "payload";
import path from "node:path";
import mime from "mime-types";
import sharp from "sharp";
import configPromise from "@payload-config";
import { NextResponse } from "next/server";

// const composers = [
// 	{
// 		name: "<PERSON>",
// 		title: "Messe Modale en Septuor für Sopran, Alt, Flöte und Orgel",
// 	},
// 	{ name: "Albrechtsberger <PERSON>", title: "Missa in D" },
// 	{ name: "<PERSON><PERSON><PERSON><PERSON>", title: "Aftonen" },
// 	{ name: "<PERSON><PERSON><PERSON>", title: "Miserere" },
// 	{
// 		name: "<PERSON><PERSON><PERSON> und Bogendorfer Anatol",
// 		title: "Diaspora Maschine (Ars Electronica Festival 2015)",
// 	},
// 	{ name: "<PERSON><PERSON>", title: "Un Sarao de la Chacona" },
// 	{ name: "Arnalds Olafur *1986", title: "Momentary" },
// 	{ name: "<PERSON><PERSON><PERSON>", title: "Te Deum Laudamus" },
// 	{ name: "<PERSON><PERSON> *1959", title: "Tykus Tykus" },
// 	{ name: "<PERSON>", title: "<PERSON><PERSON>, wecke uns auf" },
// 	{
// 		name: "<PERSON>",
// 		title: "<PERSON> ersten Weihnachtsfeiertag aus dem Weihnachtsoratorium",
// 	},
// 	{ name: "Bach P.D.Q.", title: "The Art of the Ground Round" },
// 	{ name: "Barber Samuel", title: "Agnus Dei" },
// 	{ name: "Bárdos Lajos", title: "Cantemus!" },
// 	{ name: "Barkauskas Vytautas (1931-2020)", title: "Stabat Mater" },
// 	{ name: "Barrett Michael & Ralf Schmitt arr.", title: "Indodana" },
// 	{ name: "Becker Albert", title: "Das Volk, das im Finstern wandelt" },
// 	{
// 		name: "Beethoven Ludwig van",
// 		title: "Fantasie für Klavier, Orchester und Chor c-Moll (op. 80)",
// 	},
// 	{ name: "Bergman Erik", title: "Dreams (Part 1)" },
// 	{ name: "Bingham Judith *1952", title: "The Darkness is no Darkness" },
// 	{
// 		name: "Bogendorfer Anatol und Androsch Peter",
// 		title: "Diaspora Maschine (Ars Electronica Festival 2015)",
// 	},
// 	{ name: "Brahms Johannes", title: "O Heiland, reiß die Himmel auf" },
// 	{
// 		name: "Brahms Johannes",
// 		title: "Warum ist das Licht gegeben dem Mühseligen",
// 	},
// 	{ name: "Britten Benjamin", title: "A Hymn to the Virgin" },
// 	{ name: "Bruckner Anton", title: "Afferentur, WAB 1" },
// 	{
// 		name: "Cage John, Reich Steve, Riley Terry arr. Hard-Chor DJ Set",
// 		title: "Dreammunio",
// 	},
// 	{ name: "Camargo Martina", title: "Las Olas de la Mar" },
// 	{ name: "Cech Christoph", title: "Sprachschenkung unbesteuert" },
// 	{ name: "Christiansen Paul", title: "My Song in the Night" },
// 	{ name: "Coldplay", title: "Christmas Lights" },
// 	{ name: "Copland Aaron", title: "Have Mercy On Us, O My Lord" },
// 	{ name: "Cornelius Peter", title: "Bußlied" },
// 	{
// 		name: "Craig Eela",
// 		title: "Missa Universalis (Orchesterbearbeitung Thomas Mandel)",
// 	},
// 	{ name: "David Johann Nepomuk", title: "Deutsche Messe op. 42" },
// 	{ name: "Dawson William L.", title: "Soon Ah Will Be Done" },
// 	{ name: "de Machaut Guillaume", title: "Messe de Nostre Dame" },
// 	{ name: "de Morales Cristobal", title: "Parce Mihi Domine" },
// 	{
// 		name: "de Morales Cristobal",
// 		title: "Parce Mihi Domine (mit Sopransaxophon)",
// 	},
// 	{ name: "Desprez Josquin", title: "Absalon Fili Mi" },
// 	{ name: "Doppelbauer Johann Friedrich", title: "versch. Kanons" },
// 	{ name: "Dorsey Thomas / arr. Sevier Arnold", title: "Precious Lord" },
// 	{
// 		name: "Elgar Edward *1857-1934 /arr. Cameron John *1944",
// 		title: "Lux Aeterna",
// 	},
// 	{ name: "Escalada Oscar", title: "Tangueando" },
// 	{ name: "Eśenvalds Eriks", title: "Stars" },
// 	{ name: "Fischer Ernst", title: "Spanische Nächte" },
// 	{ name: "Fjellheim Frode", title: "Vuelie" },
// 	{ name: "Forrest Dan", title: "Come to Me" },
// 	{ name: "Frieberger Rupert Gottfried", title: "Ave Maria" },
// 	{ name: "Gabrieli Giovanni", title: "In Ecclesiis" },
// 	{ name: "Gallus Jacobus", title: "Duo Seraphim" },
// 	{ name: "Giesen Matthias", title: "Psalm 90 für Sopran, Holzblock und Chor" },
// 	{ name: "Gilkyson Eliza", title: "Requiem (Arr. Craig Hella Johnson)" },
// 	{ name: "Gjeilo Ola *1978", title: "Dark Night of the Soul" },
// 	{ name: "Hall A. E.", title: "My Evaline" },
// 	{ name: "Haydn Joseph", title: "Die Himmel erzählen (aus: Der Schöpfung)" },
// 	{ name: "Heap Imogen", title: "Hide and Seek" },
// 	{
// 		name: "Heiller Anton",
// 		title: "Nicht Knechte, sondern meine Freunde nenne ich euch",
// 	},
// 	{
// 		name: "Hensel Fanny",
// 		title:
// 			"Gartenlieder. Sechs Gesänge für Sopran, Alt, Tenor und Bass, op. 3 (1847)",
// 	},
// 	{
// 		name: "Hernández-Morales Juan Manuel / Camargo Cayetano",
// 		title: "Las Olas de la Mar",
// 	},
// 	{ name: "Herndler Christoph", title: "Fugenmasse" },
// 	{ name: "Hochreither Johann B.", title: "Ave Maria Stella" },
// 	{ name: "Hogan Moses", title: "Hold on!" },
// 	{
// 		name: "Holst Gustav (1874-1934)",
// 		title:
// 			"The Planets (Die Planeten). Suite für großes Orchester, op. 32 (1914-16)",
// 	},
// 	{ name: "Holten Bo", title: "First Snow" },
// 	{ name: "Hosp Albert", title: "Josua Fit The Battle" },
// 	{ name: "Isaac Heinrich", title: "Innsbruck, ich muß dich lassen" },
// 	{ name: "Ives Charles", title: "Make a Joyful Noise Unto The Lord" },
// 	{ name: "Jackson Gabriel", title: "I Gaze Upon You" },
// 	{ name: "Janczak Pjotr", title: "Kyrie" },
// 	{ name: "Jenkins Karl", title: "And the Mother did Weep" },
// 	{ name: "Jennefelt Thomas", title: "Villarosa Sarialdi" },
// 	{
// 		name: "Jungwirth Rudolf",
// 		title: "Da Jesus an dem Kreuze stund (UA, März 2010)",
// 	},
// 	{ name: "Källman Stefan", title: "Gede Nibo" },
// 	{ name: "Kollermandel *1979, *1965", title: "Ad Astra (2024)" },
// 	{ name: "Kõrvits Tõnu", title: "In Paradisum" },
// 	{
// 		name: "Kreutzer Conradin",
// 		title: "Sechs Lieder für vier Männerstimmen, op. 79",
// 	},
// 	{ name: "Kropfreiter Augustinus Franz", title: "Nah ist" },
// 	{ name: "La Rocca Frank", title: "O magnum mysterium" },
// 	{ name: "Lang David (*1957)", title: "Again" },
// 	{ name: "Länger Manfred", title: "Der Mond ist aufgegangen" },
// 	{ name: "Lauridsen Morten", title: "O come, let us sing unto the Lord" },
// 	{ name: "Lawes William", title: "Drink tonight of the moonshine bright" },
// 	{ name: "Leite Marcos", title: "Tres cantos nativos" },
// 	{
// 		name: "Leitner Ernst Ludwig",
// 		title:
// 			"5. Sinfonie „Den Manen Anton Bruckners“ nach dem gleichnamigen Text von Josef Weinheber (UA, Oktober 2017)",
// 	},
// 	{
// 		name: "Leontovych Mykola",
// 		title: "Carol of the bells (Arrangement von Peter Wilhousky)",
// 	},
// 	{ name: "Ligeti György", title: "Éjszaka" },
// 	{ name: "Machaut Guillaume de", title: "La Messe de Nostre Dame" },
// 	{ name: "Mahler Gustav", title: "Die zwei blauen Augen" },
// 	{ name: "Mandel Thomas", title: "Und du sagst" },
// 	{ name: "Mangold Carl Amand", title: "Wanderers Nachtlied" },
// 	{ name: "Mealor Paul", title: "Ubi Caritas" },
// 	{
// 		name: "Mendelssohn Bartholdy Felix",
// 		title: "Denn er hat seinen Engeln empfohlen",
// 	},
// 	{ name: "Merten Hans Christian", title: "Ihr Lichter, führet uns" },
// 	{ name: "Miskinis Vytautas", title: "Dum Medium Silentium" },
// 	{
// 		name: "Moitzi Florian",
// 		title: "Missa Caelestis (Uraufführung 2025, Linz)",
// 	},
// 	{ name: "Monteverdi Claudio", title: "Messa à 4 Voci da Capella" },
// 	{ name: "Mozart Wolfgang Amadeus", title: "Ave Verum Corpus, KV 618" },
// 	{ name: "Muehleisen John", title: "Eat Your Vegetables" },
// 	{ name: "Nørgård Per", title: "Abendlied" },
// 	{ name: "Nørgård Per", title: "Winter Hymn" },
// 	{ name: "Nussbaumer Georg", title: "Mund zu! Ohren zu!" },
// 	{
// 		name: "Nystedt Knut (1915-2014)",
// 		title: "Miserere, op. 140 für 16-stimmigen Chor",
// 	},
// 	{ name: "Nystedt Knut", title: "O Crux" },
// 	{ name: "Orff Carl", title: "Carmina Burana" },
// 	{ name: "Palestrina Giovanni Pierluigi da", title: "Missa Papae Marcelli" },
// 	{ name: "Pamintuan John August", title: "De Profundis, op. 5, Nr. 5" },
// 	{ name: "Park Owain", title: "Caelos Ascendit Hodie" },
// 	{ name: "Pärt Arvo", title: "Beatus Petronius" },
// 	{ name: "Paulus Stephen", title: "The Road Home" },
// 	{ name: "Pentatonix Arrangement", title: "Mary Did You Know" },
// 	{ name: "Poulenc Francis", title: "Quatre Motets pour le Temps de Noel" },
// 	{ name: "Preinfalk Bernd", title: "Stuttgart 21" },
// 	{
// 		name: "Pressl Hermann Markus",
// 		title: "Asralda für 5 und 7 Stimmen und javanesischen Gong (1982)",
// 	},
// 	{ name: "Purcell Henry", title: "Hear my Prayer" },
// 	{
// 		name: "Rautavaara Einojuhani",
// 		title:
// 			"Suite de Lorca: Cancion de Jinete, El Grito, La Luna Asoma, Malaguena",
// 	},
// 	{
// 		name: "Rheinberger Josef Gabriel (1839-1901)",
// 		title: "Abendlied aus: Drei geistliche Gesänge op. 69, Nr.3",
// 	},
// 	{ name: "Rossini Gioachino", title: "O Salutaris Hostia" },
// 	{ name: "Rossini Gioachino", title: "Stabat Mater" },
// 	{ name: "Runestad Jake", title: "Nyon Nyon" },
// 	{ name: "Rutter John", title: "A Prayer of St. Patrick" },
// 	{
// 		name: "Sanström Jan",
// 		title: "Es ist ein Ros' entsprungen (von Michael Praetorius)",
// 	},
// 	{ name: "Sauseng Wolfgang", title: "Psalm 131" },
// 	{ name: "Savall Jordi", title: "A la Vida Bona" },
// 	{ name: "Schein Johann Hermann", title: "Die mit Tränen säen" },
// 	{ name: "Schein Johann Hermann", title: "O Herr, ich bin dein Knecht" },
// 	{ name: "Schitter Walter", title: "I tua, was i will" },
// 	{ name: "Schmidinger Helmut", title: "Gefülltes Gansl (UA, November 2010)" },
// 	{ name: "Schmidinger Helmut", title: "Blunzenknödl (UA, September 2010)" },
// 	{
// 		name: "Schönberg Arnold",
// 		title:
// 			"3 Volksliedsätze: Schein uns, du liebe Sonne, Es gingen zwei Gespielen gut, Herzlieblich Lieb, durch Scheiden",
// 	},
// 	{ name: "Schönberg Arnold", title: "Friede auf Erden, op. 13" },
// 	{ name: "Schronen Alwin", title: "O gäb's doch Sterne" },
// 	{
// 		name: "Schubert Franz",
// 		title: "Der Entfernten für vierstimmigen Männerchor a cappella, D 331",
// 	},
// 	{
// 		name: "Schumann Robert",
// 		title:
// 			"7 Ritornelle in kanonischen Weisen für mehrstimmigen Männerchor a cappella, op. 65 (1847)",
// 	},
// 	{ name: "Schumann Robert", title: "Schön Rohtraut" },
// 	{ name: "Schütz Heinrich", title: "An den Wassern zu Babel" },
// 	{ name: "Schütz Heinrich", title: "Jauchzet dem Herren" },
// 	{ name: "Schwertsik Kurt", title: "Sonnengesang" },
// 	{ name: "Sisask Urmas", title: "Oremus (aus Gloria Patri)" },
// 	{ name: "Sulzer Balduin", title: "A lustigö Eicht" },
// 	{ name: "Takemitsu Tōru", title: "Vocalise I und II (aus Wind horse)" },
// 	{ name: "Tavener John", title: "Shûnya" },
// 	{ name: "The Mainstreeters", title: "Coney Island Baby" },
// 	{ name: "The Real Group", title: "Acappella in Acapulco" },
// 	{ name: "Toch Ernst", title: "Die geographische Fuge" },
// 	{ name: "Tormis Veljo", title: "Lauliku lapsepõli" },
// 	{ name: "Tormis Veljo", title: "Raua Needmine (Curse upon iron)" },
// 	{ name: "Tulev Toivo (*1958)", title: "So Shall He Descend" },
// 	{ name: "Vasks Peteris (*1946)", title: "Plainscapes" },
// 	{ name: "von Bingen Hildegard", title: "Te Lucis Ante Terminum" },
// 	{
// 		name: "von Neukomm Sigismund",
// 		title: "Messe de Requiem für Chor, Klappentrompete, Hörner & Posaunen",
// 	},
// 	{ name: "Wagner David", title: "In Gmunden (UA, April 2012)" },
// 	{
// 		name: "Wagner Richard",
// 		title:
// 			"Einzugsmarsch und Chor der Ritter und Edelfrauen aus der Oper „Tannhäuser“ (2. Akt, 4. Szene)",
// 	},
// 	{
// 		name: "Wagner Richard",
// 		title:
// 			"Ansprache des Hans Sachs „Verachtet mir die Meister nicht“ aus der Oper „Die Meistersinger von Nürnberg)",
// 	},
// 	{
// 		name: "Waldek Gunter",
// 		title: "Leuchte dem, der mich schaut (UA, Juni 2010)",
// 	},
// 	{ name: "Waldek Gunter", title: "Stürme" },
// 	{ name: "Webern Anton", title: "Dormi Jesu (aus 5 Kanons op. 16)" },
// 	{ name: "Whitacre Eric", title: "Enjoy The Silence" },
// 	{ name: "Williams John", title: "Dry Your Tears (mit Blasorchester)" },
// 	{ name: "Wolfe Julia", title: "Guard My Tongue" },
// 	{ name: "Zeisl Eric", title: "Am Abend (UA, Mai 2007)" },
// 	{
// 		name: "Zöllner Carl Friedrich",
// 		title: "„Des Müllers Lust und Leid“ in 6 Gesängen aus der „Schönen",
// 	},
// ];

// const concerts = [
// 	{
// 		title:
// 			"„Bruckner-Blech“ Hard-Chor Men | Eröffnungskonzert der St. Florianer Brucknertage",
// 		slug: "bruckner-blech-hard-chor-men-eroeffnungskonzert-der-st-florianer-brucknertage",
// 		where: "Augustiner Chorherrenstift St. Florian | Marmorsaal",
// 		dates: [{ date: "2025-08-17T17:00:00.000Z", startTime: "19:30" }],
// 	},
// 	{
// 		title: "Klassische Klangwolke 25",
// 		slug: "klassische-klangwolke-25",
// 		where: "Brucknerhaus Linz | Großer Saal",
// 		dates: [{ date: "2025-09-07T17:00:00.000Z", startTime: "19:00" }],
// 	},
// 	{
// 		title: "ARVO PÄRT 90 | Berliner Messe",
// 		slug: "arvo-paert-90-berliner-messe",
// 		where: "Vereinshaus - Besední dům | Brünn, Tschechien",
// 		dates: [
// 			{ date: "2025-11-06T18:00:00.000Z", startTime: "19:00" },
// 			{ date: "2025-11-07T18:00:00.000Z", startTime: "19:00" },
// 		],
// 	},
// 	{
// 		title: "Missa Caelestis",
// 		slug: "missa-caelestis",
// 		where: "Friedenskirche | Wildbergstraße 30, 4040 Linz",
// 		dates: [{ date: "2025-05-18T17:30:00.000Z", startTime: "19:30" }],
// 	},
// 	{
// 		title: "Stars - Zwischen den Welten | musica sacra",
// 		slug: "bruckners-d-moll-messe-musica-sacra",
// 		where: "Friedenskirche | Wildbergstraße 30, 4040 Linz",
// 		dates: [{ date: "2024-11-24T17:00:00.000Z", startTime: "18:00" }],
// 	},
// 	{
// 		title: "Bruckner Motetten Crash 2",
// 		slug: "bruckner-motetten-crash-2",
// 		where: "Neuer Dom | Linz",
// 		dates: [{ date: "2024-10-18T18:00:00.000Z", startTime: "20:00" }],
// 	},
// 	{
// 		title: "Bruckner's Festkantate - Preiset den Herrn",
// 		slug: "bruckners-festkantate-preiset-den-herrn",
// 		where: "Mariendom Linz",
// 		dates: [{ date: "2024-10-03T18:00:00.000Z", startTime: "20:00" }],
// 	},
// 	{
// 		title: "DER GESUNGENE HORIZONT - Salzkammer(sc)hall 3",
// 		slug: "der-gesungene-horizont-salzkammerschall-3",
// 		where: "Offensee | Fahrnau, 4802 Ebensee",
// 		dates: [{ date: "2024-09-29T13:00:00.000Z", startTime: "15:00" }],
// 	},
// 	{
// 		title: "Bruckners d-moll-Messe | Brucknerfest 2024",
// 		slug: "bruckners-d-moll-messe-brucknerfest-2024",
// 		where: "Alter Dom | Linz",
// 		dates: [{ date: "2024-09-20T17:30:00.000Z", startTime: "19:30" }],
// 	},
// 	{
// 		title: "Brucknerfest 2024 | Festakt zur Eröffnung",
// 		slug: "festakt-zur-eroeffnung-brucknerfest-2024",
// 		where: "Brucknerhaus Linz | Großer Saal",
// 		dates: [{ date: "2024-09-08T08:30:00.000Z", startTime: "10:30" }],
// 	},
// 	{
// 		title: "Te Deum | Bruckners Geburtstag",
// 		slug: "te-deum-bruckners-geburtstag",
// 		where: "Augustiner Chorherrenstift St. Florian | Stiftsbasilika",
// 		dates: [{ date: "2024-09-04T09:00:00.000Z", startTime: "11:00 Uhr" }],
// 	},
// 	{
// 		title: "Hard-Chor versus Machine",
// 		slug: "hard-chor-versus-machine",
// 		where: "Pfarrkirche Bad Kreuzen, 4362 Bad Kreuzen",
// 		dates: [{ date: "2024-08-08T17:00:00.000Z", startTime: "19:00 Uhr" }],
// 	},
// 	{
// 		title: "Bruckner & Smetana 200 | Festival Krumlov",
// 		slug: "bruckner-smetana-200-festival-krumlov",
// 		where:
// 			"Wallfahrtskirche Maria Schnee beim Heiligen Stein | 382 41 Dolní Dvořiště-Kaplice 1, Tschechien",
// 		dates: [{ date: "2024-08-01T17:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Bruckner Resound 2.0 | Lange Nacht der Bühnen",
// 		slug: "bruckner-resound-2-0",
// 		where: "Alter Dom | Linz",
// 		dates: [{ date: "2024-06-22T17:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Bruckner's Salz",
// 		slug: "bruckners-salz",
// 		where: "Salinen Austria | Steinkogelstraße 30, 4802 Ebensee",
// 		dates: [{ date: "2024-06-15T17:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Vom Gesang mit Gebläs´ | musica sacra",
// 		slug: "vom-gesang-mit-geblaes-musica-sacra",
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2024-04-14T15:00:00.000Z", startTime: "17:00 Uhr" }],
// 	},
// 	{
// 		title: "50 Jahre Brucknerhaus | Empfang des Bürgermeisters",
// 		slug: "50-jahre-brucknerhaus-empfang-des-buergermeisters",
// 		where: "Brucknerhaus Linz",
// 		dates: [{ date: "2024-03-23T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Karl Jenkins | One World - Welturaufführung",
// 		slug: "karl-jenkins-one-world-welturauffuehrung",
// 		where: "Brucknerhaus Linz | Großer Saal",
// 		dates: [{ date: "2023-11-19T17:00:00.000Z", startTime: "18:00 Uhr" }],
// 	},
// 	{
// 		title: "Workshop: Bruckner zum Singen 3 | Die großen Motetten",
// 		slug: "workshop-bruckner-zum-singen-3-die-grossen-motetten",
// 		where: "Borg Honauerstraße, Linz | Honauerstraße 24, 4020 Linz",
// 		dates: [{ date: "2023-10-01T12:00:00.000Z", startTime: "14:00 Uhr" }],
// 	},
// 	{
// 		title: "Bruckner Resound - Bruckner und die Männer",
// 		slug: "bruckner-resound-bruckner-und-die-maenner",
// 		where:
// 			"Anton Bruckner Privatuniversität, Großer Saal | Hagenstraße 57, 4040 Linz",
// 		dates: [{ date: "2023-06-30T17:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Virgencita - Música Mexicana | Ort der Begegnung der Elisabethinen",
// 		slug: "mexiko-programm-elisabethinen-ort-der-begegnung",
// 		where:
// 			"Ort der Begegnung | Klosterkirche der Elisabethinen Linz | Bethlehemstraße 23, 4020 Linz",
// 		dates: [{ date: "2023-06-13T17:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},

// 	{
// 		title: "Dark Night | Musica Sacra",
// 		slug: "dark-night-musica-sacra",
// 		where: "Minoritenkirche | Klosterstraße 7, 4020 Linz",
// 		dates: [{ date: "2023-05-07T15:00:00.000Z", startTime: "17:00 Uhr" }],
// 	},
// 	{
// 		title: "SABO|TAGE in Concert feat. Hard-Chor",
// 		slug: "sabotage-in-concert-feat-hard-chor",
// 		where: "Brucknerhaus Linz",
// 		dates: [{ date: "2023-04-22T18:00:00.000Z", startTime: "20:00 Uhr" }],
// 	},
// 	{
// 		title: "Mexiko | Konzerttournee | Gira a México",
// 		slug: "mexiko-tournee",
// 		where: "Mexiko City /Ciudad de México",
// 		dates: [{ date: "2023-03-31T17:00:00.000Z", startTime: "20:00 Uhr" }],
// 	},
// 	{
// 		title: "Boreyko & Prager Symphoniker | Brucknerhaus",
// 		slug: "boreyko-pragersymphoniker-brucknerhaus",
// 		where: "Brucknerhaus Linz, Großer Saal",
// 		dates: [{ date: "2023-01-10T18:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Neujahrskonzert Brucknerhaus",
// 		slug: "neujahrskonzert-brucknerhaus",
// 		where: "Brucknerhaus Linz, Großer Saal",
// 		dates: [{ date: "2023-01-01T15:00:00.000Z", startTime: "16:00 Uhr" }],
// 	},
// 	{
// 		title: "Brucknertage St. Florian | Symphoniekonzert  |  Te Deum",
// 		slug: "brucknertage-st-florian-symphoniekonzert",
// 		where: "Augustiner Chorherrenstift St. Florian, Stiftsbasilika",
// 		dates: [
// 			{ date: "2022-08-19T17:30:00.000Z", startTime: "19:30 Uhr" },
// 			{ date: "2022-08-20T17:30:00.000Z", startTime: "19:30 Uhr" },
// 		],
// 	},
// 	{
// 		title: "Brucknertage St. Florian | Eröffnungskonzert",
// 		slug: "brucknertage-st-florian-eroeffnungskonzert",
// 		where: "Augustiner Chorherrenstift St. Florian, Marmorsaal",
// 		dates: [{ date: "2022-08-14T17:30:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Freistadt in Concert",
// 		slug: "freistadt-in-concert",
// 		where: "Messehalle Freistadt",
// 		dates: [{ date: "2022-06-29T15:00:00.000Z", startTime: "20:00 Uhr" }],
// 	},
// 	{
// 		title: "Beispielsweisen",
// 		slug: "beispielsweisen",
// 		where: "Veranstaltungszentrum, Gunskirchen",
// 		dates: [{ date: "2022-06-11T15:00:00.000Z", startTime: "17:00 Uhr" }],
// 	},
// 	{
// 		title: "So shall he descend | Linz & Brünn",
// 		slug: "toivo-tulev",
// 		where: "Musica Sacra Linz | Friedenskirche, Linz",
// 		dates: [{ date: "2022-04-10T14:00:00.000Z", startTime: "16:00 Uhr" }],
// 	},
// 	{
// 		title: "Weihnachtsmesse im Alten Dom",
// 		skipImage: true,
// 		slug: "konzerte-2020",
// 		cancelled: true,
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2020-12-25T15:00:00.000Z", startTime: "10:30 Uhr" }],
// 	},
// 	{
// 		title: "Hard-Chor feat. Kredance",
// 		skipImage: true,
// 		slug: "konzerte-2020",
// 		cancelled: true,
// 		where: "Messehalle, Freistadt",
// 		dates: [{ date: "2020-08-07T15:00:00.000Z", startTime: "19:00 Uhr" }],
// 	},
// 	{
// 		title: "Vokaton - „Park 2.0“",
// 		skipImage: true,
// 		slug: "konzerte-2020",
// 		cancelled: true,
// 		where: "Salzhof, Freistadt",
// 		dates: [{ date: "2020-05-30T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "„Park“ | Musica Sacra",
// 		skipImage: true,
// 		slug: "konzerte-2020",
// 		cancelled: true,
// 		where: "Ursulinenkirche, Linz",
// 		dates: [{ date: "2020-03-26T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Eela Craig",
// 		skipImage: true,
// 		slug: "konzerte-2020",
// 		where: "Brucknerhaus, Linz",
// 		dates: [{ date: "2020-03-05T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Hochamt Christtag",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2019-12-25T15:00:00.000Z", startTime: "10:30 Uhr" }],
// 	},
// 	{
// 		title: "Festkonzert „100 Jahre Linzer Konzertverein“",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Großer Saal, Brucknerhaus, Linz",
// 		dates: [{ date: "2019-12-04T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "One Take Sessions - Festival",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Café Central, Linz",
// 		dates: [{ date: "2019-11-23T15:00:00.000Z", startTime: "18:00 Uhr" }],
// 	},
// 	{
// 		title:
// 			"Internationales Brucknerfest 2019 - 150 Jahre Bruckner e-Moll-Messe (UA 1869)",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Mariendom, Linz",
// 		dates: [{ date: "2019-09-29T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "Hochamt im Mariendom",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Mariendom, Linz",
// 		dates: [{ date: "2019-09-28T15:00:00.000Z", startTime: "18:15 Uhr" }],
// 	},
// 	{
// 		title: "Woodstock der Blasmusik",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Hauptbühne, Ort im Innkreis",
// 		dates: [{ date: "2019-06-30T15:00:00.000Z", startTime: "10:00 Uhr" }],
// 	},
// 	{
// 		title: "Hochamt Ostersonntag",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2019-04-21T15:00:00.000Z", startTime: "10:30 Uhr" }],
// 	},
// 	{
// 		title: "Cori Spezzati - Musica Sacra",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2019-03-31T15:00:00.000Z", startTime: "17:00 Uhr" }],
// 	},
// 	{
// 		title:
// 			"„Su fu lu Kol kir nia“ (Und du sagst? I lie!) | Reihe zeitgenössische Musik",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Konzertsaal, LMS Wels",
// 		dates: [{ date: "2019-01-25T15:00:00.000Z", startTime: "19:30 Uhr" }],
// 	},
// 	{
// 		title: "„Su fu lu Kol kir nia“ (Und du sagst? I lie!)",
// 		skipImage: true,
// 		slug: "konzerte-2019",
// 		where: "Gläserner Saal / Magna Auditorium, Musikverein Wien",
// 		dates: [{ date: "2019-01-17T15:00:00.000Z", startTime: "20:00 Uhr" }],
// 	},
// 	{
// 		title: "Hochamt Christtag",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2018-12-25T15:00:00.000Z", startTime: "10:30 Uhr" }],
// 	},
// 	{
// 		title: "Musikalischer Adventkalender | Brucknerhaus",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Brucknerhaus, Foyer Mittlerer Saal, Linz",
// 		dates: [{ date: "2018-12-17T15:00:00.000Z", startTime: "18:00" }],
// 	},
// 	{
// 		title: "Internationales Brucknerfest 2018",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Großer Saal, Brucknerhaus, Linz",
// 		dates: [{ date: "2018-10-06T15:00:00.000Z", startTime: "19:30" }],
// 	},
// 	{
// 		title:
// 			"Klassik am Dom mit Martin Grubinger & The Percussive Planet Ensemble",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Domplatz Linz",
// 		dates: [{ date: "2018-07-05T15:00:00.000Z", startTime: "20:45" }],
// 	},
// 	{
// 		title: "Aufnahme mit dem Brucknerorchester Linz unter Markus Poschner",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Stiftsbasilika, St. Florian",
// 		dates: [{ date: "2018-06-30T15:00:00.000Z", startTime: "20:00" }],
// 	},
// 	{
// 		title: "Ort der Begegnung",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Klosterkirche der Elisabethinen, Linz",
// 		dates: [{ date: "2018-06-18T15:00:00.000Z", startTime: "19:30" }],
// 	},
// 	{
// 		title: "HURT",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Stadtpfarrkirche, Eferding",
// 		dates: [{ date: "2018-06-03T15:00:00.000Z", startTime: "19:00" }],
// 	},
// 	{
// 		title: "Bobby McFerrin “Circle Songs” feat. Hard-Chor",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Großer Saal, Brucknerhaus, Linz",
// 		dates: [{ date: "2018-04-29T15:00:00.000Z", startTime: "19:30" }],
// 	},
// 	{
// 		title: "SchuPärt",
// 		skipImage: true,
// 		slug: "konzerte-2018",
// 		where: "Alter Dom, Linz",
// 		dates: [{ date: "2018-04-28T15:00:00.000Z", startTime: "19:30" }],
// 	},
// ];

// const imageURLs = [
// 	"https://www.hard-chor.at/wp-content/uploads/2025/05/Men-only-Wahl-High-Res-Ret-6105.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2025/05/Rock-Outfit-Group-Shot-Wahl-High-Res-Ret-6265.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2025/05/Katzen-Stimmgabeln-Location-1-Wahl-High-Res-Ret-6924-II.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-141.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/08/Bruenn_2022-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/09/AEC-Alturfahr-215-116.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/11/AEC-Alturfahr-27-92.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/AEC-Alturfahr-27-921-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2023/01/AEC_Hard-Chor-Gross.jpeg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-141.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/08/2022_08_14_HC-Maennerchor_Brucknertage-2022_Eroeffnungskonzert-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2023/01/AEC_Hard-Chor-Gross.jpeg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/04/HC-Bruenn_12.04.22-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-14.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/08/2022_08_14_HC-Maennerchor_Brucknertage-2022_Eroeffnungskonzert-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/11/AEC-Alturfahr-27-92.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2023/03/Brucknerhaus-Gross-1.jpeg",
// 	"https://www.hard-chor.at/wp-content/uploads/2023/08/World-Choir-for-Peace_Jenkins_by-Dagmar-Titsch-Gross.jpeg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/AEC-Alturfahr-93-101-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/291282125_390798529581948_4995474942345558329_n.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/AEC-Alturfahr-93-101-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2023/01/AEC_Hard-Chor-Gross.jpeg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/09/AEC-Alturfahr-215-116.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2023/02/Hard-Chor-goes-Mexiko-Gross.jpeg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/HC_2017-11-26-21.21.01-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/08/Bruenn_2022-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-141.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/08/HC-Maennerchor_07.22-Gross.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/OETICKET_Freistadt_in_C.webp",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/07/280127159_10166283351555607_3815037855794400697_n.jpg",
// 	"https://www.hard-chor.at/wp-content/uploads/2022/04/HC-Bruenn_12.04.22-Gross.jpg",
// ];

const concerts = [
	{
		title: "„Bruckner-Blech“ Hard-Chor Men",
		slug: "bruckner-blech-hard-chor-men-eroeffnungskonzert-der-st-florianer-brucknertage",
		where: "Augustiner Chorherrenstift St. Florian | Marmorsaal",
		dates: [{ date: "2025-08-17T17:00:00.000Z", startTime: "19:30" }],
		subline: "Eröffnungskonzert der St. Florianer Brucknertage",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2025/05/Men-only-Wahl-High-Res-Ret-6105.jpg",
	},
	{
		title: "Klassische Klangwolke 25",
		slug: "klassische-klangwolke-25",
		where: "Brucknerhaus Linz | Großer Saal",
		dates: [{ date: "2025-09-07T17:00:00.000Z", startTime: "19:00" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2025/05/Rock-Outfit-Group-Shot-Wahl-High-Res-Ret-6265.jpg",
	},
	{
		title: "ARVO PÄRT 90",
		slug: "arvo-paert-90-berliner-messe",
		where: "Vereinshaus - Besední dům | Brünn, Tschechien",
		dates: [
			{ date: "2025-11-06T18:00:00.000Z", startTime: "19:00" },
			{ date: "2025-11-07T18:00:00.000Z", startTime: "19:00" },
		],
		subline: "Berliner Messe",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2025/05/Katzen-Stimmgabeln-Location-1-Wahl-High-Res-Ret-6924-II.jpg",
	},
	{
		title: "Missa Caelestis",
		slug: "missa-caelestis",
		where: "Friedenskirche | Wildbergstraße 30, 4040 Linz",
		dates: [{ date: "2025-05-18T17:30:00.000Z", startTime: "19:30" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-141.jpg",
	},
	{
		title: "Stars - Zwischen den Welten",
		slug: "bruckners-d-moll-messe-musica-sacra",
		where: "Friedenskirche | Wildbergstraße 30, 4040 Linz",
		dates: [{ date: "2024-11-24T17:00:00.000Z", startTime: "18:00" }],
		subline: "musica sacra",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/08/Bruenn_2022-Gross.jpg",
	},
	{
		title: "Bruckner Motetten Crash 2",
		slug: "bruckner-motetten-crash-2",
		where: "Neuer Dom | Linz",
		dates: [{ date: "2024-10-18T18:00:00.000Z", startTime: "20:00" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/09/AEC-Alturfahr-215-116.jpg",
	},
	{
		title: "Bruckner's Festkantate - Preiset den Herrn",
		slug: "bruckners-festkantate-preiset-den-herrn",
		where: "Mariendom Linz",
		dates: [{ date: "2024-10-03T18:00:00.000Z", startTime: "20:00" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/11/AEC-Alturfahr-27-92.jpg",
	},
	{
		title: "DER GESUNGENE HORIZONT - Salzkammer(sc)hall 3",
		slug: "der-gesungene-horizont-salzkammerschall-3",
		where: "Offensee | Fahrnau, 4802 Ebensee",
		dates: [{ date: "2024-09-29T13:00:00.000Z", startTime: "15:00" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/AEC-Alturfahr-27-921-Gross.jpg",
	},
	{
		title: "Bruckners d-moll-Messe",
		slug: "bruckners-d-moll-messe-brucknerfest-2024",
		where: "Alter Dom | Linz",
		dates: [{ date: "2024-09-20T17:30:00.000Z", startTime: "19:30" }],
		subline: "Brucknerfest 2024",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2023/01/AEC_Hard-Chor-Gross.jpeg",
	},
	{
		title: "Brucknerfest 2024",
		slug: "festakt-zur-eroeffnung-brucknerfest-2024",
		where: "Brucknerhaus Linz | Großer Saal",
		dates: [{ date: "2024-09-08T08:30:00.000Z", startTime: "10:30" }],
		subline: "Festakt zur Eröffnung",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-141.jpg",
	},
	{
		title: "Te Deum",
		slug: "te-deum-bruckners-geburtstag",
		where: "Augustiner Chorherrenstift St. Florian | Stiftsbasilika",
		dates: [{ date: "2024-09-04T09:00:00.000Z", startTime: "11:00 Uhr" }],
		subline: "Bruckners Geburtstag",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/08/2022_08_14_HC-Maennerchor_Brucknertage-2022_Eroeffnungskonzert-Gross.jpg",
	},
	{
		title: "Hard-Chor versus Machine",
		slug: "hard-chor-versus-machine",
		where: "Pfarrkirche Bad Kreuzen, 4362 Bad Kreuzen",
		dates: [{ date: "2024-08-08T17:00:00.000Z", startTime: "19:00 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2023/01/AEC_Hard-Chor-Gross.jpeg",
	},
	{
		title: "Bruckner & Smetana 200",
		slug: "bruckner-smetana-200-festival-krumlov",
		where:
			"Wallfahrtskirche Maria Schnee beim Heiligen Stein | 382 41 Dolní Dvořiště-Kaplice 1, Tschechien",
		dates: [{ date: "2024-08-01T17:30:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Festival Krumlov",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/04/HC-Bruenn_12.04.22-Gross.jpg",
	},
	{
		title: "Bruckner Resound 2.0",
		slug: "bruckner-resound-2-0",
		where: "Alter Dom | Linz",
		dates: [{ date: "2024-06-22T17:30:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Lange Nacht der Bühnen",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-14.jpg",
	},
	{
		title: "Bruckner's Salz",
		slug: "bruckners-salz",
		where: "Salinen Austria | Steinkogelstraße 30, 4802 Ebensee",
		dates: [{ date: "2024-06-15T17:30:00.000Z", startTime: "19:30 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/08/2022_08_14_HC-Maennerchor_Brucknertage-2022_Eroeffnungskonzert-Gross.jpg",
	},
	{
		title: "Vom Gesang mit Gebläs´",
		slug: "vom-gesang-mit-geblaes-musica-sacra",
		where: "Alter Dom, Linz",
		dates: [{ date: "2024-04-14T15:00:00.000Z", startTime: "17:00 Uhr" }],
		subline: "musica sacra",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/11/AEC-Alturfahr-27-92.jpg",
	},
	{
		title: "50 Jahre Brucknerhaus",
		slug: "50-jahre-brucknerhaus-empfang-des-buergermeisters",
		where: "Brucknerhaus Linz",
		dates: [{ date: "2024-03-23T15:00:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Empfang des Bürgermeisters",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2023/03/Brucknerhaus-Gross-1.jpeg",
	},
	{
		title: "Karl Jenkins",
		slug: "karl-jenkins-one-world-welturauffuehrung",
		where: "Brucknerhaus Linz | Großer Saal",
		dates: [{ date: "2023-11-19T17:00:00.000Z", startTime: "18:00 Uhr" }],
		subline: "One World - Welturaufführung",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2023/08/World-Choir-for-Peace_Jenkins_by-Dagmar-Titsch-Gross.jpeg",
	},
	{
		title: "Workshop: Bruckner zum Singen 3",
		slug: "workshop-bruckner-zum-singen-3-die-grossen-motetten",
		where: "Borg Honauerstraße, Linz | Honauerstraße 24, 4020 Linz",
		dates: [{ date: "2023-10-01T12:00:00.000Z", startTime: "14:00 Uhr" }],
		subline: "Die großen Motetten",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/AEC-Alturfahr-93-101-Gross.jpg",
	},
	{
		title: "Bruckner Resound - Bruckner und die Männer",
		slug: "bruckner-resound-bruckner-und-die-maenner",
		where:
			"Anton Bruckner Privatuniversität, Großer Saal | Hagenstraße 57, 4040 Linz",
		dates: [{ date: "2023-06-30T17:30:00.000Z", startTime: "19:30 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/291282125_390798529581948_4995474942345558329_n.jpg",
	},
	{
		title: "Virgencita - Música Mexicana",
		slug: "mexiko-programm-elisabethinen-ort-der-begegnung",
		where:
			"Ort der Begegnung | Klosterkirche der Elisabethinen Linz | Bethlehemstraße 23, 4020 Linz",
		dates: [{ date: "2023-06-13T17:30:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Ort der Begegnung der Elisabethinen",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/AEC-Alturfahr-93-101-Gross.jpg",
	},
	{
		title: "Dark Night",
		slug: "dark-night-musica-sacra",
		where: "Minoritenkirche | Klosterstraße 7, 4020 Linz",
		dates: [{ date: "2023-05-07T15:00:00.000Z", startTime: "17:00 Uhr" }],
		subline: "Musica Sacra",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2023/01/AEC_Hard-Chor-Gross.jpeg",
	},
	{
		title: "SABO|TAGE in Concert feat. Hard-Chor",
		slug: "sabotage-in-concert-feat-hard-chor",
		where: "Brucknerhaus Linz",
		dates: [{ date: "2023-04-22T18:00:00.000Z", startTime: "20:00 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/09/AEC-Alturfahr-215-116.jpg",
	},
	{
		title: "Mexiko",
		slug: "mexiko-tournee",
		where: "Mexiko City /Ciudad de México",
		dates: [{ date: "2023-03-31T17:00:00.000Z", startTime: "20:00 Uhr" }],
		subline: "Konzerttournee",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2023/02/Hard-Chor-goes-Mexiko-Gross.jpeg",
	},
	{
		title: "Boreyko & Prager Symphoniker",
		slug: "boreyko-pragersymphoniker-brucknerhaus",
		where: "Brucknerhaus Linz, Großer Saal",
		dates: [{ date: "2023-01-10T18:30:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Brucknerhaus",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/HC_2017-11-26-21.21.01-Gross.jpg",
	},
	{
		title: "Neujahrskonzert Brucknerhaus",
		slug: "neujahrskonzert-brucknerhaus",
		where: "Brucknerhaus Linz, Großer Saal",
		dates: [{ date: "2023-01-01T15:00:00.000Z", startTime: "16:00 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/08/Bruenn_2022-Gross.jpg",
	},
	{
		title: "Brucknertage St. Florian",
		slug: "brucknertage-st-florian-symphoniekonzert",
		where: "Augustiner Chorherrenstift St. Florian, Stiftsbasilika",
		dates: [
			{ date: "2022-08-19T17:30:00.000Z", startTime: "19:30 Uhr" },
			{ date: "2022-08-20T17:30:00.000Z", startTime: "19:30 Uhr" },
		],
		subline: "Symphoniekonzert ",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2012/09/Brucknerhaus-85-141.jpg",
	},
	{
		title: "Brucknertage St. Florian",
		slug: "brucknertage-st-florian-eroeffnungskonzert",
		where: "Augustiner Chorherrenstift St. Florian, Marmorsaal",
		dates: [{ date: "2022-08-14T17:30:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Eröffnungskonzert",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/08/HC-Maennerchor_07.22-Gross.jpg",
	},
	{
		title: "Freistadt in Concert",
		slug: "freistadt-in-concert",
		where: "Messehalle Freistadt",
		dates: [{ date: "2022-06-29T15:00:00.000Z", startTime: "20:00 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/OETICKET_Freistadt_in_C.webp",
	},
	{
		title: "Beispielsweisen",
		slug: "beispielsweisen",
		where: "Veranstaltungszentrum, Gunskirchen",
		dates: [{ date: "2022-06-11T15:00:00.000Z", startTime: "17:00 Uhr" }],
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/07/280127159_10166283351555607_3815037855794400697_n.jpg",
	},
	{
		title: "So shall he descend",
		slug: "toivo-tulev",
		where: "Musica Sacra Linz | Friedenskirche, Linz",
		dates: [{ date: "2022-04-10T14:00:00.000Z", startTime: "16:00 Uhr" }],
		subline: "Linz & Brünn",
		imageURL:
			"https://www.hard-chor.at/wp-content/uploads/2022/04/HC-Bruenn_12.04.22-Gross.jpg",
	},
	{
		title: "Weihnachtsmesse im Alten Dom",
		slug: "konzerte-2020",
		cancelled: true,
		where: "Alter Dom, Linz",
		dates: [{ date: "2020-12-25T15:00:00.000Z", startTime: "10:30 Uhr" }],
	},
	{
		title: "Hard-Chor feat. Kredance",
		slug: "konzerte-2020",
		cancelled: true,
		where: "Messehalle, Freistadt",
		dates: [{ date: "2020-08-07T15:00:00.000Z", startTime: "19:00 Uhr" }],
	},
	{
		title: "Vokaton - „Park 2.0“",
		slug: "konzerte-2020",
		cancelled: true,
		where: "Salzhof, Freistadt",
		dates: [{ date: "2020-05-30T15:00:00.000Z", startTime: "19:30 Uhr" }],
	},
	{
		title: "„Park“",
		slug: "konzerte-2020",
		cancelled: true,
		where: "Ursulinenkirche, Linz",
		dates: [{ date: "2020-03-26T15:00:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Musica Sacra",
	},
	{
		title: "Eela Craig",
		slug: "konzerte-2020",
		where: "Brucknerhaus, Linz",
		dates: [{ date: "2020-03-05T15:00:00.000Z", startTime: "19:30 Uhr" }],
	},
	{
		title: "Hochamt Christtag",
		slug: "konzerte-2019",
		where: "Alter Dom, Linz",
		dates: [{ date: "2019-12-25T15:00:00.000Z", startTime: "10:30 Uhr" }],
	},
	{
		title: "Festkonzert „100 Jahre Linzer Konzertverein“",
		slug: "konzerte-2019",
		where: "Großer Saal, Brucknerhaus, Linz",
		dates: [{ date: "2019-12-04T15:00:00.000Z", startTime: "19:30 Uhr" }],
	},
	{
		title: "One Take Sessions - Festival",
		slug: "konzerte-2019",
		where: "Café Central, Linz",
		dates: [{ date: "2019-11-23T15:00:00.000Z", startTime: "18:00 Uhr" }],
	},
	{
		title:
			"Internationales Brucknerfest 2019 - 150 Jahre Bruckner e-Moll-Messe (UA 1869)",
		slug: "konzerte-2019",
		where: "Mariendom, Linz",
		dates: [{ date: "2019-09-29T15:00:00.000Z", startTime: "19:30 Uhr" }],
	},
	{
		title: "Hochamt im Mariendom",
		slug: "konzerte-2019",
		where: "Mariendom, Linz",
		dates: [{ date: "2019-09-28T15:00:00.000Z", startTime: "18:15 Uhr" }],
	},
	{
		title: "Woodstock der Blasmusik",
		slug: "konzerte-2019",
		where: "Hauptbühne, Ort im Innkreis",
		dates: [{ date: "2019-06-30T15:00:00.000Z", startTime: "10:00 Uhr" }],
	},
	{
		title: "Hochamt Ostersonntag",
		slug: "konzerte-2019",
		where: "Alter Dom, Linz",
		dates: [{ date: "2019-04-21T15:00:00.000Z", startTime: "10:30 Uhr" }],
	},
	{
		title: "Cori Spezzati - Musica Sacra",
		slug: "konzerte-2019",
		where: "Alter Dom, Linz",
		dates: [{ date: "2019-03-31T15:00:00.000Z", startTime: "17:00 Uhr" }],
	},
	{
		title: "„Su fu lu Kol kir nia“ (Und du sagst? I lie!)",
		slug: "konzerte-2019",
		where: "Konzertsaal, LMS Wels",
		dates: [{ date: "2019-01-25T15:00:00.000Z", startTime: "19:30 Uhr" }],
		subline: "Reihe zeitgenössische Musik",
	},
	{
		title: "„Su fu lu Kol kir nia“ (Und du sagst? I lie!)",
		slug: "konzerte-2019",
		where: "Gläserner Saal / Magna Auditorium, Musikverein Wien",
		dates: [{ date: "2019-01-17T15:00:00.000Z", startTime: "20:00 Uhr" }],
	},
	{
		title: "Hochamt Christtag",
		slug: "konzerte-2018",
		where: "Alter Dom, Linz",
		dates: [{ date: "2018-12-25T15:00:00.000Z", startTime: "10:30 Uhr" }],
	},
	{
		title: "Musikalischer Adventkalender",
		slug: "konzerte-2018",
		where: "Brucknerhaus, Foyer Mittlerer Saal, Linz",
		dates: [{ date: "2018-12-17T15:00:00.000Z", startTime: "18:00" }],
		subline: "Brucknerhaus",
	},
	{
		title: "Internationales Brucknerfest 2018",
		slug: "konzerte-2018",
		where: "Großer Saal, Brucknerhaus, Linz",
		dates: [{ date: "2018-10-06T15:00:00.000Z", startTime: "19:30" }],
	},
	{
		title:
			"Klassik am Dom mit Martin Grubinger & The Percussive Planet Ensemble",
		slug: "konzerte-2018",
		where: "Domplatz Linz",
		dates: [{ date: "2018-07-05T15:00:00.000Z", startTime: "20:45" }],
	},
	{
		title: "Aufnahme mit dem Brucknerorchester Linz unter Markus Poschner",
		slug: "konzerte-2018",
		where: "Stiftsbasilika, St. Florian",
		dates: [{ date: "2018-06-30T15:00:00.000Z", startTime: "20:00" }],
	},
	{
		title: "Ort der Begegnung",
		slug: "konzerte-2018",
		where: "Klosterkirche der Elisabethinen, Linz",
		dates: [{ date: "2018-06-18T15:00:00.000Z", startTime: "19:30" }],
	},
	{
		title: "HURT",
		slug: "konzerte-2018",
		where: "Stadtpfarrkirche, Eferding",
		dates: [{ date: "2018-06-03T15:00:00.000Z", startTime: "19:00" }],
	},
	{
		title: "Bobby McFerrin “Circle Songs” feat. Hard-Chor",
		slug: "konzerte-2018",
		where: "Großer Saal, Brucknerhaus, Linz",
		dates: [{ date: "2018-04-29T15:00:00.000Z", startTime: "19:30" }],
	},
	{
		title: "SchuPärt",
		slug: "konzerte-2018",
		where: "Alter Dom, Linz",
		dates: [{ date: "2018-04-28T15:00:00.000Z", startTime: "19:30" }],
	},
];

// type WpPost = {
// 	title: { rendered: string };
// 	slug: string;
// 	content: { rendered: string };
// };

// type ParsedConcert = {
// 	title: string;
// 	slug: string;
// 	where: string;
// 	dates: {
// 		date: string;
// 		startTime: string;
// 	}[];
// };

// function parseWpPost(post: WpPost): ParsedConcert {
// 	const html = post.content.rendered;

// 	const extractField = (label: string): string | null => {
// 		const regex = new RegExp(`<strong>${label}:<\\/strong>\\s*([^<]+)`, "i");
// 		const match = html.match(regex);
// 		return match ? match[1].trim() : null;
// 	};

// 	const when = extractField("Wann");
// 	const where = extractField("Wo") ?? "";
// 	const dates: ParsedConcert["dates"] = [];

// 	if (when) {
// 		// Beispiel: "Do, 6. & Fr, 7. November 2025 | 19.00"
// 		const [datesPart, timePartRaw] = when.split("|").map((s) => s.trim());

// 		const time = timePartRaw?.replace(".", ":") ?? null;

// 		// Versuche z. B. ["6.", "7. November 2025"]
// 		const dateMatches = [
// 			...datesPart.matchAll(/(\d{1,2})(?:\.|\.\s*&)?\s*/g),
// 		].map((m) => m[1]);
// 		const fullDateText = datesPart.match(
// 			/(\d{1,2})[^\d]+(\d{1,2})\.\s*([A-Za-zäöüÄÖÜ]+)\s+(\d{4})/,
// 		);

// 		const monthYearMatch = datesPart.match(/([A-Za-zäöüÄÖÜ]+)\s+(\d{4})/);
// 		if (dateMatches.length > 0 && monthYearMatch && time) {
// 			const [_, monthName, yearStr] = monthYearMatch;
// 			const monthMap: Record<string, number> = {
// 				Jänner: 0,
// 				Februar: 1,
// 				März: 2,
// 				April: 3,
// 				Mai: 4,
// 				Juni: 5,
// 				Juli: 6,
// 				August: 7,
// 				September: 8,
// 				Oktober: 9,
// 				November: 10,
// 				Dezember: 11,
// 			};

// 			const year = Number.parseInt(yearStr);
// 			const month = monthMap[monthName];
// 			if (month === undefined || Number.isNaN(year)) {
// 				console.warn("Unrecognized month or year:", monthName, year);
// 				return {
// 					title: post.title.rendered,
// 					slug: post.slug,
// 					where,
// 					dates: [],
// 				}; // skip if broken
// 			}

// 			for (const dayStr of dateMatches) {
// 				const day = Number.parseInt(dayStr);
// 				const [hourStr, minuteStr] = time.split(":");
// 				const date = new Date(
// 					year,
// 					month,
// 					day,
// 					Number.parseInt(hourStr),
// 					Number.parseInt(minuteStr),
// 				);
// 				if (!Number.isNaN(date.getTime())) {
// 					dates.push({
// 						date: date.toISOString(),
// 						startTime: time,
// 					});
// 				}
// 			}
// 		}
// 	}

// 	return {
// 		title: post.title.rendered,
// 		slug: post.slug,
// 		where,
// 		dates,
// 	};
// }

// function parseAll(posts: WpPost[]): ParsedConcert[] {
// 	return posts.map(parseWpPost);
// }

// async function getImageURLs(): Promise<string[]> {
// 	const imageURLs: string[] = [];

// 	for (const c of concerts_raw) {
// 		if (c._links["wp:featuredmedia"]?.[0]?.href) {
// 			const url = c._links["wp:featuredmedia"][0].href;
// 			const res = await fetch(url);
// 			const json = await res.json();

// 			imageURLs.push(json.guid.rendered);
// 		}
// 	}

// 	return imageURLs;
// }

// function combineImagesWithConcerts() {
// 	return concerts.map((c, i) => {
// 		const title = c.title.split(" | ")[0];
// 		const subline = c.title.split(" | ")[1];
// 		return {
// 			...c,
// 			title,
// 			subline,
// 			imageURL: imageURLs[i],
// 		};
// 	});
// }

// function validate(concerts) {
// 	const errors = [];

// 	for (const c of concerts) {
// 		if (!c.dates.length) {
// 			errors.push({ title: c.title, msg: "NO DATES" });
// 		}

// 		for (const d of c.dates) {
// 			const dateStr = d.date;

// 			try {
// 				const isoDateTimeRegex =
// 					/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z$/;

// 				if (typeof dateStr !== "string" || !isoDateTimeRegex.test(dateStr)) {
// 					errors.push({ title: c.title, msg: "INVALID TIME FORMAT" });
// 					continue;
// 				}

// 				const date = new Date(dateStr);
// 				if (Number.isNaN(date.getTime())) {
// 					errors.push({ title: c.title, msg: "INVALID TIME PARSE" });
// 				}

// 				// Optional strict check — usually not needed unless you're worried about canonical form
// 				// if (date.toISOString() !== dateStr) {
// 				//   errors.push({ title: c.title, msg: "NORMALIZATION MISMATCH" });
// 				// }
// 			} catch {
// 				errors.push({ title: c.title, msg: "INVALID TIME ERROR" });
// 			}
// 		}

// 		if (!c.where) {
// 			errors.push({ title: c.title, msg: "NO WHERE" });
// 		}
// 		if (!c.imageURL && !c.skipImage) {
// 			errors.push({ title: c.title, msg: "NO IMAGE" });
// 		}
// 		if (!c.title) {
// 			errors.push({ title: c.title, msg: "NO TITLE" });
// 		}
// 	}

// 	return errors;
// }

export const GET = async () => {
	try {
		const payloadInstance = await getPayload({ config: configPromise });
		const concertsReversed = [...concerts].reverse();

		for (const concert of concertsReversed) {
			if (!concert.imageURL) {
				await payloadInstance.create({
					collection: "concerts",
					data: {
						dates: concert.dates,
						where: concert.where,
						title: concert.title,
						subline: concert.subline,
						cancelled: concert.cancelled,
					},
				});
				continue;
			}

			const response = await fetch(concert.imageURL);
			if (!response.ok) {
				console.error(`Failed to fetch image from ${concert.imageURL}`);
				continue;
			}

			const arrayBuffer = await response.arrayBuffer();
			const originalBuffer = Buffer.from(arrayBuffer as ArrayBuffer);

			const url = new URL(concert.imageURL);
			const filename = path.basename(url.pathname);
			const ext = path.extname(filename).toLowerCase();
			const mimeType = response.headers.get("content-type") || mime.lookup(ext) || "image/jpeg";

			let compressedBuffer = originalBuffer;
			try {
				const sharpInstance = sharp(originalBuffer).resize({
					width: 2000,
					withoutEnlargement: true,
				});

				if (mimeType === "image/jpeg" || mimeType === "image/jpg") {
					compressedBuffer = await sharpInstance.jpeg({ quality: 50 }).toBuffer();
				} else if (mimeType === "image/png") {
					compressedBuffer = await sharpInstance.png({ compressionLevel: 9 }).toBuffer();
				}
			} catch (err) {
				console.warn(`Image compression failed for ${filename}, using original.`, err);
			}

			const media = await payloadInstance.create({
				collection: "media",
				file: {
					data: compressedBuffer,
					mimetype: mimeType,
					name: filename,
					size: compressedBuffer.length,
				},
				data: {
					alt: concert.title,
				},
			});

			await payloadInstance.create({
				collection: "concerts",
				data: {
					dates: concert.dates,
					where: concert.where,
					title: concert.title,
					image: media.id,
					subline: concert.subline,
					cancelled: concert.cancelled,
				},
			});
		}

		return NextResponse.json({ success: true });
	} catch (error) {
		console.error(error);
		return NextResponse.json(
			{ error: "INTERNAL_SERVER_ERROR" },
			{ status: 500 },
		);
	}
};